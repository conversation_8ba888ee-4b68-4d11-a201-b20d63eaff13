'''
实验名称：高级轮廓检测与分析
实验平台：01Studio CanMV K230
说明：完整的图像处理管道，包含多种滤波方法和轮廓分析
功能特性：
1. 多种图像预处理方法
2. 自适应阈值处理
3. 形态学操作
4. 轮廓特征分析
5. 实时参数调整
'''

import time, os, sys, gc, math
from media.sensor import *
from media.display import *
from media.media import *

# 配置参数
class Config:
    # 图像尺寸
    WIDTH = 320
    HEIGHT = 240
    DISPLAY_WIDTH = 800
    DISPLAY_HEIGHT = 480
    
    # 处理参数
    GAUSSIAN_SIZE = 3           # 高斯模糊核大小
    MEDIAN_SIZE = 3             # 中值滤波核大小
    BINARY_THRESHOLD = (80, 255) # 二值化阈值
    MIN_AREA = 200              # 最小轮廓面积
    MAX_AREA = 10000            # 最大轮廓面积
    
    # 形态学操作
    ERODE_TIMES = 1             # 腐蚀次数
    DILATE_TIMES = 2            # 膨胀次数

class ImageProcessor:
    """图像处理器类"""
    
    def __init__(self):
        self.config = Config()
        self.processing_mode = 0  # 处理模式：0=基础, 1=高级, 2=形态学
        
    def preprocess_image(self, img):
        """图像预处理"""
        if self.processing_mode == 0:
            # 基础处理：高斯模糊 + 二值化
            img.gaussian(self.config.GAUSSIAN_SIZE)
            img.binary([self.config.BINARY_THRESHOLD])
            
        elif self.processing_mode == 1:
            # 高级处理：中值滤波 + 高斯模糊 + 二值化
            img.median(self.config.MEDIAN_SIZE)
            img.gaussian(self.config.GAUSSIAN_SIZE)
            img.binary([self.config.BINARY_THRESHOLD])
            
        elif self.processing_mode == 2:
            # 形态学处理：滤波 + 二值化 + 腐蚀 + 膨胀
            img.gaussian(self.config.GAUSSIAN_SIZE)
            img.binary([self.config.BINARY_THRESHOLD])
            img.erode(self.config.ERODE_TIMES)
            img.dilate(self.config.DILATE_TIMES)
            
        return img
    
    def analyze_contour(self, blob):
        """分析轮廓特征"""
        # 计算轮廓特征
        area = blob.pixels()
        perimeter = 2 * (blob.w() + blob.h())  # 近似周长
        
        # 计算圆形度 (4π*面积/周长²)
        if perimeter > 0:
            circularity = (4 * math.pi * area) / (perimeter * perimeter)
        else:
            circularity = 0
            
        # 计算长宽比
        if blob.h() > 0:
            aspect_ratio = blob.w() / blob.h()
        else:
            aspect_ratio = 0
            
        # 计算密度 (面积/边界框面积)
        bbox_area = blob.w() * blob.h()
        if bbox_area > 0:
            density = area / bbox_area
        else:
            density = 0
            
        return {
            'area': area,
            'perimeter': perimeter,
            'circularity': circularity,
            'aspect_ratio': aspect_ratio,
            'density': density
        }
    
    def classify_shape(self, features):
        """基于特征分类形状"""
        circularity = features['circularity']
        aspect_ratio = features['aspect_ratio']
        
        if circularity > 0.7:
            return "Circle", (0, 255, 0)  # 绿色
        elif 0.8 < aspect_ratio < 1.2:
            return "Square", (255, 255, 0)  # 黄色
        elif aspect_ratio > 2.0:
            return "Rectangle", (255, 0, 255)  # 紫色
        else:
            return "Irregular", (255, 0, 0)  # 红色

class ContourDetector:
    """轮廓检测器类"""
    
    def __init__(self):
        self.processor = ImageProcessor()
        self.sensor = None
        self.frame_count = 0
        
    def init_camera(self):
        """初始化摄像头"""
        self.sensor = Sensor(width=Config.WIDTH, height=Config.HEIGHT)
        self.sensor.reset()
        self.sensor.set_framesize(width=Config.WIDTH, height=Config.HEIGHT)
        self.sensor.set_pixformat(Sensor.RGB565)
        
        Display.init(Display.ST7701, width=Config.DISPLAY_WIDTH, 
                    height=Config.DISPLAY_HEIGHT, to_ide=True)
        MediaManager.init()
        self.sensor.run()
        
        print("Camera initialized")
    
    def deinit_camera(self):
        """释放摄像头资源"""
        if self.sensor:
            self.sensor.stop()
        Display.deinit()
        MediaManager.deinit()
        print("Camera deinitialized")
    
    def detect_contours(self, img, original_img):
        """检测并分析轮廓"""
        contours = []
        
        # 查找连通区域
        blobs = img.find_blobs([(255, 255)], 
                              pixels_threshold=self.processor.config.MIN_AREA,
                              area_threshold=self.processor.config.MIN_AREA)
        
        if blobs:
            for i, blob in enumerate(blobs):
                # 过滤面积
                if blob.pixels() > self.processor.config.MAX_AREA:
                    continue
                    
                # 分析轮廓特征
                features = self.processor.analyze_contour(blob)
                shape_name, color = self.processor.classify_shape(features)
                
                # 绘制轮廓
                original_img.draw_rectangle(blob.rect(), color=color, thickness=2)
                original_img.draw_cross(blob.cx(), blob.cy(), color=color, size=8, thickness=2)
                
                # 显示形状信息
                info_text = f"{shape_name}"
                original_img.draw_string_advanced(blob.x(), blob.y()-25, 16, info_text, color=color)
                
                # 显示面积信息
                area_text = f"A:{blob.pixels()}"
                original_img.draw_string_advanced(blob.x(), blob.y()-10, 12, area_text, color=(255, 255, 255))
                
                contours.append({
                    'blob': blob,
                    'features': features,
                    'shape': shape_name
                })
        
        return contours
    
    def draw_info_panel(self, img, contours, fps):
        """绘制信息面板"""
        # 背景
        img.draw_rectangle(5, 5, 200, 80, color=(0, 0, 0), fill=True)
        img.draw_rectangle(5, 5, 200, 80, color=(255, 255, 255), thickness=1)
        
        # 基本信息
        img.draw_string_advanced(10, 10, 16, f"FPS: {fps:.1f}", color=(255, 255, 255))
        img.draw_string_advanced(10, 25, 16, f"Contours: {len(contours)}", color=(255, 255, 255))
        img.draw_string_advanced(10, 40, 16, f"Mode: {self.processor.processing_mode}", color=(255, 255, 255))
        
        # 形状统计
        shapes = {}
        for contour in contours:
            shape = contour['shape']
            shapes[shape] = shapes.get(shape, 0) + 1
        
        y_offset = 55
        for shape, count in shapes.items():
            img.draw_string_advanced(10, y_offset, 12, f"{shape}: {count}", color=(255, 255, 0))
            y_offset += 15
    
    def run(self):
        """主运行循环"""
        clock = time.clock()
        
        print("Starting advanced contour detection...")
        print("Processing modes: 0=Basic, 1=Advanced, 2=Morphology")
        
        try:
            while True:
                clock.tick()
                self.frame_count += 1
                
                # 获取图像
                original_img = self.sensor.snapshot()
                processed_img = original_img.copy()
                
                # 切换处理模式（每200帧切换一次）
                if self.frame_count % 200 == 0:
                    self.processor.processing_mode = (self.processor.processing_mode + 1) % 3
                    print(f"Switched to processing mode: {self.processor.processing_mode}")
                
                # 图像处理
                processed_img = self.processor.preprocess_image(processed_img)
                
                # 轮廓检测
                contours = self.detect_contours(processed_img, original_img)
                
                # 绘制信息面板
                fps = clock.fps()
                self.draw_info_panel(original_img, contours, fps)
                
                # 显示结果
                x_offset = (Config.DISPLAY_WIDTH - Config.WIDTH) // 2
                y_offset = (Config.DISPLAY_HEIGHT - Config.HEIGHT) // 2
                Display.show_image(original_img, x=x_offset, y=y_offset)
                
                # 统计输出
                if self.frame_count % 60 == 0:
                    print(f"Frame {self.frame_count}: FPS={fps:.1f}, Contours={len(contours)}")
                    if contours:
                        for i, contour in enumerate(contours[:3]):  # 只显示前3个
                            features = contour['features']
                            print(f"  Contour {i}: {contour['shape']}, "
                                  f"Area={features['area']}, "
                                  f"Circularity={features['circularity']:.2f}")
                
                # 内存管理
                if self.frame_count % 20 == 0:
                    gc.collect()
                    
        except KeyboardInterrupt:
            print("Detection stopped by user")
        except Exception as e:
            print(f"Error: {e}")

def main():
    """主函数"""
    print("=== Advanced Contour Detection and Analysis ===")
    print("Features:")
    print("- Multi-stage image preprocessing")
    print("- Shape classification")
    print("- Feature analysis")
    print("- Real-time mode switching")
    print()
    
    detector = ContourDetector()
    
    try:
        detector.init_camera()
        detector.run()
    except Exception as e:
        print(f"Exception: {e}")
    finally:
        detector.deinit_camera()
        print("Program ended")

if __name__ == "__main__":
    main()

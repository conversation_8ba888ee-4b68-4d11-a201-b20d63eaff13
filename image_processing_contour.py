'''
实验名称：图像处理与轮廓检测
实验平台：01Studio CanMV K230
说明：从摄像头获取图像，进行灰度转换、高斯模糊、二值化处理，最后获取物体轮廓
功能流程：
1. 摄像头图像采集
2. 灰度转换
3. 高斯模糊处理
4. 二值化处理
5. 轮廓检测与绘制
'''

import time, os, sys
import gc

from media.sensor import * #导入sensor模块，使用摄像头相关接口
from media.display import * #导入display模块，使用display相关接口
from media.media import * #导入media模块，使用meida相关接口

# 图像处理参数配置
DETECT_WIDTH = 320   # 检测图像宽度，较小分辨率提升处理速度
DETECT_HEIGHT = 240  # 检测图像高度
DISPLAY_WIDTH = 800  # 显示宽度
DISPLAY_HEIGHT = 480 # 显示高度

# 图像处理参数
GAUSSIAN_KERNEL_SIZE = 3    # 高斯模糊核大小
BINARY_THRESHOLD = (100, 255)  # 二值化阈值范围
MIN_CONTOUR_AREA = 100      # 最小轮廓面积，过滤小噪点

def camera_init():
    """初始化摄像头和显示"""
    global sensor
    
    # 构建摄像头对象
    sensor = Sensor(width=DETECT_WIDTH, height=DETECT_HEIGHT)
    sensor.reset() # 复位和初始化摄像头
    
    # 设置帧大小和像素格式
    sensor.set_framesize(width=DETECT_WIDTH, height=DETECT_HEIGHT)
    sensor.set_pixformat(Sensor.RGB565) # 设置输出图像格式
    
    # 初始化显示 - 使用3.5寸MIPI屏幕
    Display.init(Display.ST7701, width=DISPLAY_WIDTH, height=DISPLAY_HEIGHT, to_ide=True)
    
    # 初始化media资源管理器
    MediaManager.init()
    
    # 启动sensor
    sensor.run()
    
    print("Camera initialized successfully")

def camera_deinit():
    """释放摄像头资源"""
    global sensor
    
    # 停止sensor
    sensor.stop()
    # 释放显示资源
    Display.deinit()
    # 释放media资源
    MediaManager.deinit()
    
    print("Camera deinitialized")

def process_image(img):
    """
    图像处理流程
    1. 转换为灰度图像
    2. 高斯模糊
    3. 二值化
    4. 轮廓检测
    """
    
    # 步骤1: 转换为灰度图像
    # CanMV中RGB565图像可以直接进行处理，无需显式转换灰度
    
    # 步骤2: 高斯模糊处理，减少噪声
    img.gaussian(GAUSSIAN_KERNEL_SIZE)
    
    # 步骤3: 二值化处理
    # 将图像转换为黑白二值图像
    img.binary([BINARY_THRESHOLD])
    
    return img

def find_and_draw_contours(img, original_img):
    """
    查找并绘制轮廓
    使用find_blobs函数检测连通区域作为轮廓
    """
    contours_found = 0
    
    # 使用find_blobs检测白色区域（二值化后的前景对象）
    # 在二值化图像中，白色区域代表前景对象
    blobs = img.find_blobs([(255, 255)], pixels_threshold=MIN_CONTOUR_AREA, area_threshold=MIN_CONTOUR_AREA)
    
    if blobs:
        for blob in blobs:
            # 在原始图像上绘制轮廓矩形框
            original_img.draw_rectangle(blob.rect(), color=(255, 0, 0), thickness=2)
            
            # 绘制轮廓中心点
            original_img.draw_cross(blob.cx(), blob.cy(), color=(0, 255, 0), size=10, thickness=2)
            
            # 绘制轮廓信息文本
            info_text = f"Area:{blob.pixels()}"
            original_img.draw_string_advanced(blob.x(), blob.y()-20, 16, info_text, color=(255, 255, 0))
            
            contours_found += 1
    
    return contours_found

def main_loop():
    """主处理循环"""
    clock = time.clock()
    frame_count = 0
    
    print("Starting image processing loop...")
    print("Press Ctrl+C to stop")
    
    try:
        while True:
            clock.tick()
            frame_count += 1
            
            # 获取原始图像
            original_img = sensor.snapshot()
            
            # 创建处理图像的副本
            processed_img = original_img.copy()
            
            # 执行图像处理流程
            processed_img = process_image(processed_img)
            
            # 在处理后的图像上查找轮廓，并在原始图像上绘制结果
            contour_count = find_and_draw_contours(processed_img, original_img)
            
            # 在图像上显示处理信息
            fps = clock.fps()
            info_text = f"FPS:{fps:.1f} Contours:{contour_count}"
            original_img.draw_string_advanced(5, 5, 20, info_text, color=(255, 255, 255))
            
            # 显示处理结果（显示原始图像+轮廓标注）
            Display.show_image(original_img, x=round((DISPLAY_WIDTH-DETECT_WIDTH)/2), 
                             y=round((DISPLAY_HEIGHT-DETECT_HEIGHT)/2))
            
            # 每30帧打印一次统计信息
            if frame_count % 30 == 0:
                print(f"Frame {frame_count}: FPS={fps:.1f}, Contours={contour_count}")
            
            # 内存管理
            if frame_count % 10 == 0:
                gc.collect()
                
    except KeyboardInterrupt:
        print("Processing stopped by user")
    except Exception as e:
        print(f"Error occurred: {e}")

def main():
    """主函数"""
    print("=== Image Processing and Contour Detection ===")
    print("Processing pipeline:")
    print("1. Camera capture")
    print("2. Gaussian blur")
    print("3. Binary threshold")
    print("4. Contour detection")
    print("5. Result visualization")
    print()
    
    camera_is_init = False
    
    try:
        # 初始化摄像头
        camera_init()
        camera_is_init = True
        
        # 开始主处理循环
        main_loop()
        
    except Exception as e:
        print(f"Exception in main: {e}")
    finally:
        # 清理资源
        if camera_is_init:
            camera_deinit()
        print("Program ended")

if __name__ == "__main__":
    main()

'''
实验名称：YOLOv8物体检测 + HDMI显示 + 性能优化版
实验平台：01Studio CanMV K230
说明：高性能版本，通过多种优化策略提升帧率
优化策略：
1. 降低显示分辨率
2. 减少绘制复杂度
3. 优化内存使用
4. 跳帧显示
'''

from libs.PipeLine import PipeLine, ScopedTiming
from libs.AIBase import AIBase
from libs.AI2D import Ai2d
import os
import ujson
from media.media import *
from media.sensor import *
from time import *
import nncase_runtime as nn
import ulab.numpy as np
import time
import utime
import image
import random
import gc
import sys
import aidemo

# 高性能YOLOv8检测类
class OptimizedObjectDetectionApp(AIBase):
    def __init__(self,kmodel_path,labels,model_input_size,max_boxes_num,confidence_threshold=0.5,nms_threshold=0.2,rgb888p_size=[224,224],display_size=[1280,720],debug_mode=0):
        super().__init__(kmodel_path,model_input_size,rgb888p_size,debug_mode)
        self.kmodel_path=kmodel_path
        self.labels=labels
        self.model_input_size=model_input_size
        self.confidence_threshold=confidence_threshold
        self.nms_threshold=nms_threshold
        self.max_boxes_num=max_boxes_num
        self.rgb888p_size=[ALIGN_UP(rgb888p_size[0],16),rgb888p_size[1]]
        self.display_size=[ALIGN_UP(display_size[0],16),display_size[1]]
        self.debug_mode=debug_mode
        
        # 简化颜色配置，减少内存占用
        self.colors=[(255, 255, 0, 0), (255, 0, 255, 0), (255, 0, 0, 255), (255, 255, 255, 0)]
        
        # 缩放比例
        self.x_factor = float(self.rgb888p_size[0])/self.model_input_size[0]
        self.y_factor = float(self.rgb888p_size[1])/self.model_input_size[1]
        
        # Ai2d实例
        self.ai2d=Ai2d(debug_mode)
        self.ai2d.set_ai2d_dtype(nn.ai2d_format.NCHW_FMT,nn.ai2d_format.NCHW_FMT,np.uint8, np.uint8)
        
        # 性能计数器
        self.frame_count = 0
        self.display_skip = 2  # 每3帧显示1帧，提升性能

    def config_preprocess(self,input_image_size=None):
        with ScopedTiming("set preprocess config",self.debug_mode > 0):
            ai2d_input_size=input_image_size if input_image_size else self.rgb888p_size
            self.ai2d.resize(nn.interp_method.tf_bilinear, nn.interp_mode.half_pixel)
            self.ai2d.build([1,3,ai2d_input_size[1],ai2d_input_size[0]],[1,3,self.model_input_size[1],self.model_input_size[0]])

    def postprocess(self,results):
        with ScopedTiming("postprocess",self.debug_mode > 0):
            result=results[0]
            result = result.reshape((result.shape[0] * result.shape[1], result.shape[2]))
            output_data = result.transpose()
            boxes_ori = output_data[:,0:4]
            scores_ori = output_data[:,4:]
            confs_ori = np.max(scores_ori,axis=-1)
            inds_ori = np.argmax(scores_ori,axis=-1)
            boxes,scores,inds = [],[],[]
            
            for i in range(len(boxes_ori)):
                if confs_ori[i] > self.confidence_threshold:
                    scores.append(confs_ori[i])
                    inds.append(inds_ori[i])
                    x = boxes_ori[i,0]
                    y = boxes_ori[i,1]
                    w = boxes_ori[i,2]
                    h = boxes_ori[i,3]
                    left = int((x - 0.5 * w) * self.x_factor)
                    top = int((y - 0.5 * h) * self.y_factor)
                    right = int((x + 0.5 * w) * self.x_factor)
                    bottom = int((y + 0.5 * h) * self.y_factor)
                    boxes.append([left,top,right,bottom])
                    
            if len(boxes)==0:
                return []
                
            boxes = np.array(boxes)
            scores = np.array(scores)
            inds = np.array(inds)
            
            # 快速NMS
            keep = self.fast_nms(boxes,scores,self.nms_threshold)
            dets = np.concatenate((boxes, scores.reshape((len(boxes),1)), inds.reshape((len(boxes),1))), axis=1)
            dets_out = []
            for keep_i in keep:
                dets_out.append(dets[keep_i])
            dets_out = np.array(dets_out)
            dets_out = dets_out[:min(self.max_boxes_num, 10), :]  # 限制最大检测框数量
            return dets_out

    # 优化版绘制结果 - 减少绘制复杂度
    def draw_result(self,pl,dets,fps):
        self.frame_count += 1
        
        # 跳帧显示策略
        if self.frame_count % (self.display_skip + 1) != 0:
            return
            
        with ScopedTiming("display_draw",self.debug_mode >0):
            pl.osd_img.clear()
            
            # 绘制帧率 - 使用较小字体
            pl.osd_img.draw_string_advanced(10, 10, 32, f"FPS: {fps:.1f}", color=(255, 255, 255, 0))
            
            # 绘制检测框 - 简化绘制
            if dets:
                for i, det in enumerate(dets):
                    if i >= 5:  # 最多显示5个检测框
                        break
                        
                    x1, y1, x2, y2 = map(lambda x: int(round(x, 0)), det[:4])
                    x= x1*self.display_size[0] // self.rgb888p_size[0]
                    y= y1*self.display_size[1] // self.rgb888p_size[1]
                    w = (x2 - x1) * self.display_size[0] // self.rgb888p_size[0]
                    h = (y2 - y1) * self.display_size[1] // self.rgb888p_size[1]
                    
                    # 使用简化颜色和较细线条
                    color = self.colors[int(det[5]) % len(self.colors)]
                    pl.osd_img.draw_rectangle(x,y, w, h, color=color, thickness=2)
                    
                    # 简化标签显示
                    label = self.labels[int(det[5])][:8]  # 截断长标签
                    pl.osd_img.draw_string_advanced(x, y-30, 24, f"{label}", color=color)

    # 快速NMS实现
    def fast_nms(self,boxes,scores,thresh):
        if len(boxes) == 0:
            return []
            
        # 简化的NMS，只保留前10个高分检测框
        order = np.argsort(scores)[::-1][:10]
        keep = []
        
        while len(order) > 0 and len(keep) < 5:  # 最多保留5个
            i = order[0]
            keep.append(i)
            
            if len(order) == 1:
                break
                
            # 简化IoU计算
            xx1 = np.maximum(boxes[i,0], boxes[order[1:],0])
            yy1 = np.maximum(boxes[i,1], boxes[order[1:],1])
            xx2 = np.minimum(boxes[i,2], boxes[order[1:],2])
            yy2 = np.minimum(boxes[i,3], boxes[order[1:],3])
            
            w = np.maximum(0.0, xx2 - xx1)
            h = np.maximum(0.0, yy2 - yy1)
            inter = w * h
            
            area_i = (boxes[i,2] - boxes[i,0]) * (boxes[i,3] - boxes[i,1])
            area_others = (boxes[order[1:],2] - boxes[order[1:],0]) * (boxes[order[1:],3] - boxes[order[1:],1])
            
            ovr = inter / (area_i + area_others - inter)
            inds = np.where(ovr <= thresh)[0]
            order = order[inds + 1]
            
        return keep


if __name__=="__main__":
    # 性能优化配置
    display_mode='hdmi'
    display_size=[1280,720]  # 降低分辨率
    rgb888p_size=[320,320]
    
    # 模型配置
    kmodel_path="/sdcard/examples/kmodel/yolov8n_320.kmodel"
    labels = ["person", "bicycle", "car", "motorcycle", "airplane", "bus", "train", "truck", "boat", "traffic light", "fire hydrant", "stop sign", "parking meter", "bench", "bird", "cat", "dog", "horse", "sheep", "cow", "elephant", "bear", "zebra", "giraffe", "backpack", "umbrella", "handbag", "tie", "suitcase", "frisbee", "skis", "snowboard", "sports ball", "kite", "baseball bat", "baseball glove", "skateboard", "surfboard", "tennis racket", "bottle", "wine glass", "cup", "fork", "knife", "spoon", "bowl", "banana", "apple", "sandwich", "orange", "broccoli", "carrot", "hot dog", "pizza", "donut", "cake", "chair", "couch", "potted plant", "bed", "dining table", "toilet", "tv", "laptop", "mouse", "remote", "keyboard", "cell phone", "microwave", "oven", "toaster", "sink", "refrigerator", "book", "clock", "vase", "scissors", "teddy bear", "hair drier", "toothbrush"]
    
    # 优化检测参数
    confidence_threshold = 0.3  # 提高阈值，减少检测框
    nms_threshold = 0.3
    max_boxes_num = 10  # 减少最大检测框数量

    # 初始化PipeLine
    pl=PipeLine(rgb888p_size=rgb888p_size,display_size=display_size,display_mode=display_mode)
    pl.create(Sensor(width=1280, height=720))  # 匹配显示分辨率

    # 初始化优化版检测器
    ob_det=OptimizedObjectDetectionApp(kmodel_path,labels=labels,model_input_size=[320,320],max_boxes_num=max_boxes_num,confidence_threshold=confidence_threshold,nms_threshold=nms_threshold,rgb888p_size=rgb888p_size,display_size=display_size,debug_mode=0)
    ob_det.config_preprocess()

    clock = time.clock()
    
    print("Optimized YOLOv8 HDMI Detection Started...")
    print("Performance optimizations enabled:")
    print("- Resolution: 1280x720")
    print("- Skip frame display")
    print("- Simplified drawing")
    print("- Limited detection boxes")

    try:
        while True:
            clock.tick()
            
            img=pl.get_frame()
            res=ob_det.run(img)
            fps = clock.fps()
            ob_det.draw_result(pl,res,fps)
            pl.show_image()
            
            # 减少垃圾回收频率
            if ob_det.frame_count % 30 == 0:
                gc.collect()

            # 简化控制台输出
            if ob_det.frame_count % 30 == 0:  # 每30帧打印一次
                print(f"FPS: {fps:.1f}, Objects: {len(res) if res else 0}")

    except KeyboardInterrupt:
        print("Detection stopped")
    finally:
        gc.collect()
